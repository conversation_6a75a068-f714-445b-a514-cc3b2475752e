import tkinter as tk
from PIL import ImageGrab, ImageEnhance, Image
import easyocr
import pyperclip
import keyboard
import threading
import os
import numpy as np
import time

reader = easyocr.Reader(['en'])

def snip_and_extract():
    root = tk.Tk()
    root.attributes('-fullscreen', True)
    root.attributes('-alpha', 0.3)
    root.configure(bg='black')
    root.title("Snipping Tool")

    # Bring the window to front
    root.lift()
    root.attributes('-topmost', True)
    root.after(10, lambda: root.focus_force())

    canvas = tk.Canvas(root, cursor="cross", bg="gray", highlightthickness=0)
    canvas.pack(fill=tk.BOTH, expand=True)

    coords = {'x1': 0, 'y1': 0, 'x2': 0, 'y2': 0}
    rect = canvas.create_rectangle(0, 0, 0, 0, outline='red', width=2)

    def on_mouse_down(event):
        coords['x1'] = root.winfo_pointerx()
        coords['y1'] = root.winfo_pointery()
        canvas.coords(rect, coords['x1'], coords['y1'], coords['x1'], coords['y1'])

    def on_mouse_drag(event):
        x2, y2 = root.winfo_pointerx(), root.winfo_pointery()
        canvas.coords(rect, coords['x1'], coords['y1'], x2, y2)

    def on_mouse_up(event):
        coords['x2'] = root.winfo_pointerx()
        coords['y2'] = root.winfo_pointery()

        # Compute bounding box
        x1, y1 = coords['x1'], coords['y1']
        x2, y2 = coords['x2'], coords['y2']
        bbox = (min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2))

        # Hide window before capture
        root.withdraw()
        time.sleep(0.3)

        # Grab screenshot and preprocess
        img = ImageGrab.grab(bbox).convert("RGB")
        img = img.resize((img.width * 2, img.height * 2))  # Upscale for OCR
        img = img.convert("L")  # Grayscale
        img = ImageEnhance.Contrast(img).enhance(2.0)
        img = img.copy()

        # Debug: save & show image
        img.save("snip_debug.png")
        img.show()

        # OCR
        result = reader.readtext(np.array(img), detail=0)
        text = "\n".join(result)

        print("\n📄 OCR Result:")
        print("================================")
        print(text if text else "[No text detected]")
        print("================================")

        # Copy to clipboard
        pyperclip.copy(text)
        print("✅ Text copied to clipboard!")

        root.destroy()

    # Bind events
    canvas.bind("<ButtonPress-1>", on_mouse_down)
    canvas.bind("<B1-Motion>", on_mouse_drag)
    canvas.bind("<ButtonRelease-1>", on_mouse_up)

    root.mainloop()

def run_snipper():
    threading.Thread(target=snip_and_extract, daemon=True).start()

def monitor_exit():
    print("🚀 Press Ctrl+Alt+O to snip. Press Esc to exit.")
    keyboard.wait("esc")
    print("👋 Exiting...")
    os._exit(0)

# Register hotkey
keyboard.add_hotkey("ctrl+alt+o", run_snipper)

# Start exit monitor
threading.Thread(target=monitor_exit, daemon=True).start()

# Keep script alive
keyboard.wait()
