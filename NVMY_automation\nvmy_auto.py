from pywinauto.application import Application
import time

try:
    app = Application(backend="uia").start(r"D:\Training\Goutam_tool_automation\NVMY\v12.4.0\NvmyAnalyzer.exe")
    time.sleep(2)

    main_window = app.window(title="NVMY Analyzer V12.4.0")

    input_field = main_window.child_window(title="Input File", control_type="Edit").wrapper_object()
    input_field.type_keys(r"C:\path\to\your\input_file.txt")

    analyze_button = main_window.child_window(title="Analyze", control_type="Button").wrapper_object()
    analyze_button.click()

except Exception as e:
    print(f"An error occurred: {e}")

